import React, { useState } from 'react';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  MOCK_CURRENCY_TRANSFER_VIEW_DATA,
  supportLogsData
} from '../../../Mocks/MockData';
import { currencyTransferNewHeaders } from '../../../Utils/Constants/TableHeaders';

const ViewCurrencyTransfer = ({
  setPageState,
  attachmentsModal,
  setAttachmentsModal,
}) => {
  const [tableData, setTableData] = useState(MOCK_CURRENCY_TRANSFER_VIEW_DATA?.tableData);
  const [deleteModal, setDeleteModal] = useState(false);

  return (
    <>
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7 mb-4">
            <div className="row">
              <div className="col-12 col-sm-6 mb-4">
                <p className="detail-title detail-label-color mb-1">Debit Account</p>
                <p className="detail-text wrapText mb-0">
                  {`${MOCK_CURRENCY_TRANSFER_VIEW_DATA.debitAccount.party} ${MOCK_CURRENCY_TRANSFER_VIEW_DATA.debitAccount.label}`}
                </p>
              </div>
              <div className="col-12 col-sm-6 mb-4">
                <p className="detail-title detail-label-color mb-1">Credit Account</p>
                <p className="detail-text wrapText mb-0">
                  {`${MOCK_CURRENCY_TRANSFER_VIEW_DATA.creditAccount.party} ${MOCK_CURRENCY_TRANSFER_VIEW_DATA.creditAccount.label}`}
                </p>
              </div>
              <div className="col-12 col-sm-6 mb-4">
                <p className="detail-title detail-label-color mb-1">Account Title</p>
                <p className="detail-text wrapText mb-0">
                  {MOCK_CURRENCY_TRANSFER_VIEW_DATA.accountTitle.label}
                </p>
              </div>
            </div>
          </div>
        </div>
        <CustomTable
          displayCard={false}
          headers={currencyTransferNewHeaders}
          isPaginated={false}
          hideSearch
          hideItemsPerPage
        >
          <tbody>
            {tableData?.map((x, i) => (
              <tr key={i}>
                <td>{i + 1}</td>
                <td>{x.currency}</td>
                <td>{x.amount}</td>
                <td>{x.narration}</td>
                <td>{x.docType}</td>
                <td>{x.docNo}</td>
                <td>{x.bank}</td>
                <td>{x.city}</td>
                <td>{x.code}</td>
                <td>{x.action}</td>
              </tr>
            ))}
          </tbody>
        </CustomTable>
      </div>
      <VoucherNavigationBar
        actionButtons={[
          {
            text: 'Edit',
            onClick: () => {
              console.log('edit');
              setPageState('edit');
            },
          },
          {
            text: 'Delete',
            onClick: () => {
              setDeleteModal(true);
            },
            variant: 'secondaryButton',
          },
          {
            text: 'Print',
            onClick: () => {
              console.log('print');
            },
            variant: 'secondaryButton',
          },
        ]}
        onAttachmentClick={() => setAttachmentsModal(true)}
        lastVoucherHeading="Last TRQ Number"
        lastVoucherNumber={'05'}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={attachmentsModal}
        close={() => setAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          viewOnly
          item={supportLogsData[0]}
          closeUploader={() => setAttachmentsModal(false)}
        />
      </CustomModal>

      {/* Delete Suspense Voucher Modal */}
      <CustomModal
        show={deleteModal}
        close={() => {
          setDeleteModal(false); // Close the modal on cancel
        }}
        action={() => {
          console.log('delete');
          setDeleteModal(false);
        }}
        title="Delete"
        description={`Are you sure you want to delete the SVR 23?`}
        disableClick={false} // Disable modal actions while loading
      />
    </>
  );
};

export default ViewCurrencyTransfer;
