import React, { useEffect, useState } from 'react';
import { HiOutlineTrash } from 'react-icons/hi2';
import Skeleton from 'react-loading-skeleton';
import CustomInput from '../../../Components/CustomInput';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import { serialNum } from '../../../Utils/Utils';
import useCurrencyRate from '../../../Hooks/useCurrencyRate';

const CurrencyTransferRow = ({
  row,
  index,
  isDisabled,
  updateField,
  handleDeleteRow,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect,
  currencyOptions = [], // Following JournalVoucherRow pattern
  date, // Following JournalVoucherRow pattern
  bankOptions = [], // Following General.js pattern
  docTypeOptions = [], // Following General.js pattern
  cityOptions = [], // Following General.js pattern
}) => {
  // State for rate error validation (following JournalVoucherRow pattern)
  const [showRowRateError, setShowRowRateError] = useState(false);

  // Fetch currency rate using custom hook (following JournalVoucherRow pattern)
  const { data: currencyRate, isLoading: isLoadingCurrencyRate } =
    useCurrencyRate(row.currency_id, date);

  // Handle currency rate response (following JournalVoucherRow pattern)
  useEffect(() => {
    if (currencyRate?.rate) {
      if (row.amount) {
        updateField(
          row.id,
          'lc_amount',
          parseFloat(row.amount) * parseFloat(currencyRate.rate)
        );
      }
      updateField(row.id, 'rate', currencyRate.rate);
    } else if (currencyRate) {
      setCurrencyToSelect(row.currency_id);
      setShowMissingCurrencyRateModal(true);
      updateField(row.id, 'lc_amount', 0);
      updateField(row.id, 'rate', 0);
      updateField(row.id, 'currency_id', null);
    }
  }, [currencyRate?.rate]);

  // Recalculate LC amount when rate changes (following JournalVoucherRow pattern)
  useEffect(() => {
    if (row.rate && row.amount) {
      updateField(
        row.id,
        'lc_amount',
        parseFloat(row.amount) * parseFloat(row.rate)
      );
    }
  }, [row.rate]);

  const docTypeOptions = [
    { label: 'Type A', value: 'type_a' },
    { label: 'Type B', value: 'type_b' },
    { label: 'Type C', value: 'type_c' }
  ];

  const bankOptions = [
    { label: 'Bank A', value: 'bank_a' },
    { label: 'Bank B', value: 'bank_b' },
    { label: 'Bank C', value: 'bank_c' }
  ];

  const cityOptions = [
    { label: 'City A', value: 'city_a' },
    { label: 'City B', value: 'city_b' },
    { label: 'City C', value: 'city_c' }
  ];

  return (
    <tr>
      <td>{serialNum(index + 1)}</td>
      <td style={{ minWidth: '120px' }}>
        <SearchableSelect
          options={currencyOptions}
          value={row.currency}
          borderRadius={10}
          isDisabled={isDisabled}
          onChange={(selected) => {
            // Following JournalVoucherRow pattern for currency selection
            updateField(row.id, 'currency', selected.value);
            updateField(row.id, 'currency_id', selected.value); // Store currency_id for rate fetching
          }}
          placeholder="Select Currency"
        />
      </td>
      {/* Rate field (following JournalVoucherRow pattern) */}
      <td>
        {isLoadingCurrencyRate ? (
          <Skeleton duration={1} width={'100%'} baseColor="#ddd" height={16} />
        ) : (
          <CustomInput
            type={'text'}
            value={row.rate || ''}
            placeholder="Rate"
            inputClass={showRowRateError ? 'text-danger' : ''}
            disabled={isDisabled}
            error={
              showRowRateError && currencyRate?.min_range
                ? `Range: ${currencyRate?.min_range} - ${currencyRate?.max_range}`
                : ''
            }
            onChange={(e) => {
              const newRate = parseFloat(e.target.value);
              const isError =
                newRate < currencyRate?.min_range ||
                newRate > currencyRate?.max_range;
              setShowRowRateError(isError);
              updateField(row.id, 'error', isError);
              updateField(row.id, 'rate', e.target.value);
            }}
            borderRadius={10}
          />
        )}
      </td>
      <td>
        <CustomInput
          type="number"
          value={row.amount}
          placeholder="Enter Amount"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'amount', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.narration}
          placeholder="Enter Narration"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'narration', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td style={{ minWidth: '150px' }}>
        <SearchableSelect
          options={docTypeOptions}
          value={row.docType}
          borderRadius={10}
          isDisabled={isDisabled}
          onChange={(selected) => updateField(row.id, 'docType', selected.value)}
          placeholder="Select Doc Type"
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.docNo}
          placeholder="Enter Doc No"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'docNo', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td style={{ minWidth: '120px' }}>
        <SearchableSelect
          options={bankOptions}
          value={row.bank}
          borderRadius={10}
          isDisabled={isDisabled}
          onChange={(selected) => updateField(row.id, 'bank', selected.value)}
          placeholder="Select Bank"
        />
      </td>
      <td style={{ minWidth: '120px' }}>
        <SearchableSelect
          options={cityOptions}
          value={row.city}
          borderRadius={10}
          isDisabled={isDisabled}
          onChange={(selected) => updateField(row.id, 'city', selected.value)}
          placeholder="Select City"
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.code}
          placeholder="Enter Code"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'code', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <TableActionDropDown
          actions={[
            {
              name: 'Delete',
              icon: HiOutlineTrash,
              onClick: () => handleDeleteRow(row.id),
              className: 'delete',
            },
          ]}
        />
      </td>
    </tr>
  );
};

export default CurrencyTransferRow;
