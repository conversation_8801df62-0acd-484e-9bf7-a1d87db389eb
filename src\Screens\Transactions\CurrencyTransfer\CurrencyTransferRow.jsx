import React from 'react';
import { HiOutlineTrash } from 'react-icons/hi2';
import CustomInput from '../../../Components/CustomInput';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import { serialNum } from '../../../Utils/Utils';

const CurrencyTransferRow = ({
  row,
  index,
  isDisabled,
  updateField,
  handleDeleteRow,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect
}) => {
  const currencyOptions = [
    { label: 'DHS', value: 'DHS' },
    { label: 'USD', value: 'USD' },
    { label: 'EUR', value: 'EUR' },
    { label: 'ETH', value: 'ETH' }
  ];

  const docTypeOptions = [
    { label: 'Type A', value: 'type_a' },
    { label: 'Type B', value: 'type_b' },
    { label: 'Type C', value: 'type_c' }
  ];

  const bankOptions = [
    { label: 'Bank A', value: 'bank_a' },
    { label: 'Bank B', value: 'bank_b' },
    { label: 'Bank C', value: 'bank_c' }
  ];

  const cityOptions = [
    { label: 'City A', value: 'city_a' },
    { label: 'City B', value: 'city_b' },
    { label: 'City C', value: 'city_c' }
  ];

  return (
    <tr>
      <td>{serialNum(index + 1)}</td>
      <td style={{ minWidth: '120px' }}>
        <SearchableSelect
          options={currencyOptions}
          value={row.currency}
          isDisabled={isDisabled}
          onChange={(selected) => {
            console.log(selected.value);
            if (selected.value.toLowerCase() === 'eth') {
              setShowMissingCurrencyRateModal(true);
              setCurrencyToSelect('eth');
            }
            else {
              updateField(row.id, 'currency', selected.value)
            }
          }}
          placeholder="Select Currency"
        />
      </td>
      <td>
        <CustomInput
          type="number"
          value={row.amount}
          placeholder="Enter Amount"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'amount', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.narration}
          placeholder="Enter Narration"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'narration', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td style={{ minWidth: '150px' }}>
        <SearchableSelect
          options={docTypeOptions}
          value={row.docType}
          isDisabled={isDisabled}
          onChange={(selected) => updateField(row.id, 'docType', selected.value)}
          placeholder="Select Doc Type"
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.docNo}
          placeholder="Enter Doc No"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'docNo', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td style={{ minWidth: '120px' }}>
        <SearchableSelect
          options={bankOptions}
          value={row.bank}
          isDisabled={isDisabled}
          onChange={(selected) => updateField(row.id, 'bank', selected.value)}
          placeholder="Select Bank"
        />
      </td>
      <td style={{ minWidth: '120px' }}>
        <SearchableSelect
          options={cityOptions}
          value={row.city}
          isDisabled={isDisabled}
          onChange={(selected) => updateField(row.id, 'city', selected.value)}
          placeholder="Select City"
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.code}
          placeholder="Enter Code"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'code', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <TableActionDropDown
          actions={[
            {
              name: 'Delete',
              icon: HiOutlineTrash,
              onClick: () => handleDeleteRow(row.id),
              className: 'delete',
            },
          ]}
        />
      </td>
    </tr>
  );
};

export default CurrencyTransferRow;
